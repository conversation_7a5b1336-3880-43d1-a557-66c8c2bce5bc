#!/usr/bin/python
import json
import re
import sys
import os
from subprocess import Popen, PIPE
from typing import List


class DbUpgradeResult:
    MODULES = 'modules'
    REPORTING_DB = 'reporting-db'
    SUCCESS = 'success'
    STATUS = 'status'
    ACTUALMODULEVERSION = 'actualModuleVersion'
    ACTUALMODULEREVISION = 'actualModuleRevision'
    LASTFOUNDREV = 'lastFoundRev'
    OUTPUT = 'output'
    MESSAGE = 'message'

    modules = None
    success = None
    reporting_db: dict = None

    def __init__(self, result: dict):
        result.setdefault(self.MODULES, {})
        result.setdefault(self.SUCCESS, False)
        self.modules = result[self.MODULES]
        self.success = result[self.SUCCESS]
        self.reporting_db = result[self.REPORTING_DB]


def dbupgrade_ttwa(customer_name: str, pool_command: str) -> DbUpgradeResult:
    yiic = "protected/yiic"
    db_upgrade_command = "dbupgrade run"

    command = "{} -f {} -- {}".format(generate_pool_command(customer_name, pool_command), yiic, db_upgrade_command)
    print('Executing command: {}'.format(command))
    db_upgrade_result_json = os.popen(command)

    return DbUpgradeResult(
        json.loads(db_upgrade_result_json.read())
    )


def dbupgrades_ttwa(customers: list[str], pool_command: str) -> bool:
    current_dir = os.path.abspath(os.curdir)
    os.chdir('application/current/app/')
    for customer in customers:
        print("Starting DBUpgrade: {}".format(customer))
        results[customer] = dbupgrade_ttwa(customer, pool_command)
        print("DBUpgrade finished: {}".format(customer))

    result: DbUpgradeResult
    has_failed_customer = False
    for customer, result in results.items():
        if not result.success:
            has_failed_customer = True
        line_format = '{:15s}|{:10s}|{:10s}|{:10s}|{:10s}|'
        print()
        print('Customer: {} -> success {}'.format(customer, result.success))
        print(line_format.format('-' * 15, '-' * 10, '-' * 10, '-' * 10, '-' * 10))
        print(line_format.format('Module name', 'Status', 'Act. ver.', 'Act. rev.', 'Last rev.'))
        module_result: dict
        for module_name, module_result in result.modules.items():
            module_result.setdefault(DbUpgradeResult.ACTUALMODULEVERSION, '')
            module_result.setdefault(DbUpgradeResult.ACTUALMODULEREVISION, '')
            module_result.setdefault(DbUpgradeResult.LASTFOUNDREV, '')
            print(
                line_format.format(
                    module_name,
                    str(module_result[DbUpgradeResult.STATUS]),
                    str(module_result[DbUpgradeResult.ACTUALMODULEVERSION]),
                    str(module_result[DbUpgradeResult.ACTUALMODULEREVISION]),
                    str(module_result[DbUpgradeResult.LASTFOUNDREV]),
                )
            )
        print(line_format.format('-' * 15, '-' * 10, '-' * 10, '-' * 10, '-' * 10))
        print()
        print("Outputs:")
        for module_name, module_result in result.modules.items():
            output = module_result[DbUpgradeResult.OUTPUT]
            if len(output) == 0:
                continue
            print()
            print("Module: {}".format(module_name))
            print('-' * 20)
            print(output)
        print()
        reporting_db = result.reporting_db
        if DbUpgradeResult.STATUS in reporting_db.keys():
            print('Reporting DB Schema refresh status: {}'.format(reporting_db[DbUpgradeResult.STATUS]))
        if DbUpgradeResult.MESSAGE in reporting_db.keys():
            print('Reporting DB Schema refresh output: {}'.format(reporting_db[DbUpgradeResult.MESSAGE]))
    os.chdir(current_dir)

    return not has_failed_customer


def doctrine_migation(customer_name: str, pool_command: str) -> int:
    console = "bin/console"
    migration_output = Popen(
        [pool_command, '-f', console, '--', 'database:migration'],
        env={
            'CUSTOMER': customer_name
        },
        stdout=PIPE
    )
    migration_output.wait()
    stddout = migration_output.stdout.read()
    print(stddout.decode())

    return migration_output.returncode


def doctrine_migrations(customers: list[str], pool_command: str) -> bool:
    current_dir = os.path.abspath(os.curdir)
    os.chdir('application/current/app/symfony/')
    has_failed_customer = False
    for customer in customers:
        print("Starting Doctrine migration: {}".format(customer))
        result = doctrine_migation(customer, pool_command)
        print("Doctrine migration finished: {}, status: {}".format(customer, result))
        if result != 0:
            has_failed_customer = True
    os.chdir(current_dir)

    return not has_failed_customer


def generate_pool_command(customer: str, pool_command: str) -> str:
    return "CUSTOMER={} {}".format(customer, pool_command)


def find_customers_by_pattern(pattern: str) -> List[str]:
    files_list = os.listdir('application/current/app/protected/config/access/')
    pattern_compiled = re.compile(pattern)
    customers = []
    for file_name in files_list:
        match = pattern_compiled.match(file_name)
        if match:
            customers.append(match.group())

    return customers


if __name__ == "__main__":
    results = {}
    if len(sys.argv) < 3:
        print('Usage: {} hostname_pattern'.format(sys.argv[0]))
        print('Example: {} {} {}'.format(sys.argv[0], "'.*\\.login\\.hu'", 'php8.2-pool-cli'))
        exit(1)
    host_pattern = sys.argv[1]
    pool_command = sys.argv[2]
    customers = find_customers_by_pattern(host_pattern)
    ttwa_status = dbupgrades_ttwa(customers, pool_command)
    print()
    print('=' * 80)
    print()
    doctrine_status = doctrine_migrations(customers, pool_command)

    if ttwa_status and doctrine_status:
        exit(0)

    exit(1)
