pipeline {

    agent any
    options {
        copyArtifactPermission('Build/EASEBuild');
    }
    parameters {
        run(name: "EASE_BUILD_NUMBER", projectName: "Build/EASEBuild", filter: "SUCCESSFUL")
        string(name: 'STRUCTURE_DIR', trim: true, defaultValue: params.STRUCTURE_DIR ?: '')
        string(name: 'SERVER_IP', trim: true, defaultValue: params.SERVER_IP ?: '')
        string(name: 'SSH_USER', trim: true, defaultValue: params.SSH_USER ?: '')
        string(name: 'AGENT_NAME', trim: true, defaultValue: params.AGENT_NAME ?: '')
        string(name: 'DBPATCH_PATTERN', trim: true, defaultValue: params.DBPATCH_PATTERN ?: '')
        string(name: 'POOL_COMMAND', trim: true, defaultValue: params.POOL_COMMAND ?: 'php8.2-pool-cli')
    }
    environment {
        ARTIFACT_DIR = "${STRUCTURE_DIR}/artifact"
    }
    stages {
        stage("Initalize") {
            steps {
                script {
                    cleanWs()
                    sh "env | sort"
                    echo "${JOB_BASE_NAME}"
                    copyArtifacts(
                        filter: '*.zip',
                        projectName: 'Build/EASEBuild',
                        fingerprintArtifacts: true,
                        selector: specific('${EASE_BUILD_NUMBER_NUMBER}')
                    )
                    env.ARTIFACT_FILE = sh(returnStdout: true, script: 'find . -name "*.zip" -exec basename \\{} \\; | head -n 1').trim()
                }
            }
        }
        stage("Refresh deploy scripts") {
            steps {
                script {
                    checkout scmGit(
                        branches: [
                            [name: '*/master']
                        ],
                        extensions: [
                            [$class: 'SparseCheckoutPaths', sparseCheckoutPaths: [[path: 'symfony/oam/jenkins/jobs/ease_deploy']]],
                            cloneOption(noTags: true, reference: '', shallow: true),
                            [$class: 'RelativeTargetDirectory', relativeTargetDir: 'oam']
                        ],
                        userRemoteConfigs: [
                            [credentialsId: '2fa196b3-cb1b-4213-9578-e3abef2e343d', url: '**************:LoginAutonom/TTWA.git']
                        ]
                    )
                }
                sshagent(["${AGENT_NAME}"]) {
                    sh '''
                    scp -o StrictHostKeyChecking=no oam/symfony/oam/jenkins/jobs/ease_deploy/dbupgrade.py ${SSH_USER}@${SERVER_IP}:${STRUCTURE_DIR}/
                    scp -o StrictHostKeyChecking=no oam/symfony/oam/jenkins/jobs/ease_deploy/deploy.sh ${SSH_USER}@${SERVER_IP}:${STRUCTURE_DIR}/
                    scp -o StrictHostKeyChecking=no oam/symfony/oam/jenkins/jobs/ease_deploy/after_deploy.sh ${SSH_USER}@${SERVER_IP}:${STRUCTURE_DIR}/
                    scp -o StrictHostKeyChecking=no oam/symfony/oam/jenkins/jobs/ease_deploy/symlinks.txt ${SSH_USER}@${SERVER_IP}:${STRUCTURE_DIR}/
                    ssh -o StrictHostKeyChecking=no ${SSH_USER}@${SERVER_IP} "cd ${STRUCTURE_DIR}/ && chmod a+x deploy.sh"
                    '''
                }
            }
        }
        stage('Deploy') {
            steps {
                sh 'printenv | sort'
                echo "Artifact file: ${ARTIFACT_FILE}"
                sshagent(["${AGENT_NAME}"]) {
                    sh '''
                    scp -o StrictHostKeyChecking=no ttwa-${EASE_BUILD_NUMBER_NUMBER}-*.zip ${SSH_USER}@${SERVER_IP}:${ARTIFACT_DIR}/
                    ssh -o StrictHostKeyChecking=no ${SSH_USER}@${SERVER_IP} "cd ${STRUCTURE_DIR}/ && sh deploy.sh ${ARTIFACT_FILE} ${STRUCTURE_DIR}"
                    ssh -o StrictHostKeyChecking=no ${SSH_USER}@${SERVER_IP} "cd ${STRUCTURE_DIR}/ && python3.9 dbupgrade.py ${DBPATCH_PATTERN} ${POOL_COMMAND}"
                    ssh -o StrictHostKeyChecking=no ${SSH_USER}@${SERVER_IP} "cd ${STRUCTURE_DIR}/ && sh after_deploy.sh ${POOL_COMMAND} ${DBPATCH_PATTERN}"
                    '''
                }
            }
        }
    }
}
