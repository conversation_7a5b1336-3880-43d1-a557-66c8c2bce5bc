<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Extractor;

use Flow\ETL\FlowContext;
use LoginAutonom\EtlBundle\Descriptor\EtlWorkflowDescriptor;
use LoginAutonom\EtlBundle\DTO\EtlFileInfo;
use LoginAutonom\EtlBundle\DTO\EtlProviderArguments;
use LoginAutonom\EtlBundle\DTO\ExtractedData;
use LoginAutonom\EtlBundle\Enum\EtlCommonFieldNamesEnum;
use LoginAutonom\EtlBundle\Enum\EtlFileStatusEnum;
use LoginAutonom\EtlBundle\Interfaces\EtlFileBasedPagerAwareProviderInterface;
use LoginAutonom\EtlBundle\Interfaces\FlowExtractorInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class EtlFileBasedPagerAwareExtractor implements FlowExtractorInterface, LoggerAwareInterface
{
    use LoggerAwareTrait;

    public const OFFSET = 'offset';
    private array $fileMetadata = [];

    public function __construct(
        private readonly array $options,
        private readonly int $rowLimit,
        private readonly array $filePatternFields,
        private readonly EtlFileBasedPagerAwareProviderInterface $provider,
        private readonly EtlWorkflowDescriptor $etlWorkflowDescriptor
    ) {
    }

    public function extract(FlowContext $context): \Generator
    {
        $infoStorage = $this->etlWorkflowDescriptor->getInfoStorage();
        if (!$infoStorage->has(EtlFileInfo::class)) {
            $this->logger->info('Extracted file not found');
            return;
        }

        /** @var EtlFileInfo $fileInfo */
        foreach ($infoStorage->getAll(EtlFileInfo::class) as $fileInfo) {
            if ($fileInfo->getStatus() !== EtlFileStatusEnum::INIT) {
                $this->logger->info(
                    "{$fileInfo->getFilename()} file status is not INIT, skipped!"
                );
                continue;
            }
            $providerArguments = new EtlProviderArguments(
                $this->rowLimit,
                $this->options,
                info: $fileInfo
            );
            $extraFields = [];
            $patternMatch = $fileInfo->getPatternMatch();
            foreach ($this->filePatternFields as $matchNum => $filePatternField) {
                if (!isset($patternMatch[$matchNum])) {
                    continue;
                }
                $extraFields[$filePatternField] = $patternMatch[$matchNum];
            }
            $fileInfo->setStatus(EtlFileStatusEnum::PROCESSING);
            foreach ($this->provider->provideData($providerArguments) as [$rows, $offset, $metadata]) {
                $rows = array_map(static fn($item) => array_merge($item, $extraFields), $rows);
                $extractedData = new ExtractedData(
                    $rows,
                    [
                        EtlCommonFieldNamesEnum::OFFSET => $offset,
                        EtlCommonFieldNamesEnum::METADATA => $metadata,
                    ],
                );

                yield $extractedData;
            }
            $fileInfo->setStatus(EtlFileStatusEnum::SUCCESS);
        }
    }
}
