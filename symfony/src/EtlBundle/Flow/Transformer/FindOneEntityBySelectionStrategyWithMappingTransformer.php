<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\CoreBundle\Util\ValidityUtil;
use LoginAutonom\EtlBundle\Entity\EtlMapping;
use LoginAutonom\EtlBundle\Interfaces\EtlCacheAwareInterface;
use LoginAutonom\EtlBundle\Trait\EtlCacheAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\EtlSerializableTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class FindOneEntityBySelectionStrategyWithMappingTransformer implements
    Transformer,
    LoggerAwareInterface,
    EtlCacheAwareInterface
{
    use EtlCacheAwareTrait;
    use LoggerAwareTrait;
    use EtlSerializableTrait;
    use EtlCommonFunctionsTrait;

    private const MAX = 'max';
    private const MIN = 'min';
    private const FIRST = 'first';

    public function __construct(
        private readonly string $mappingField,
        private readonly string $targetField,
        private readonly string $entityClass,
        private readonly ValidityUtil $validityUtil,
        private readonly string $selectionStrategy,
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $this->logger->info('Starting transformer: ' . get_class($this), ['rows' => $rows->count()]);

        $entityCache = $this->getEntityCache($context);
        $newRows = new Rows();

        /** @var Row $row */
        foreach ($rows as $row) {
            if ($this->isRowFinal($row)) {
                $newRows = $newRows->add($row);
                continue;
            }

            if ($row->has($this->targetField)) {
                $newRows = $newRows->add($row);
                continue;
            }

            if (!$row->has($this->mappingField)) {
                $newRows = $newRows->add($row);
                continue;
            }

            /** @var EtlMapping $mapping */
            $mapping = $row->get($this->mappingField)->value();

            try {
                $validityIntervalBasedEntity = $entityCache->get($mapping->getTargetIdentifier(), $this->entityClass);
                $entities = $validityIntervalBasedEntity->getAll();
                if (empty($entities)) {
                    $newRows = $newRows->add($row);
                    continue;
                }

                $selectedEntity = $this->selectEntityByStrategy($entities);

                $newRows = $newRows->add(
                    $row->set($context->entryFactory()->create($this->targetField, $selectedEntity))
                );
            } catch (NotFoundException $e) {
                $newRows = $newRows->add($row);
                continue;
            }
        }

        $this->logger->info('Stopping transformer: ' . get_class($this), ['rows' => $newRows->count()]);

        return $newRows;
    }

    private function selectEntityByStrategy(array $entities): object
    {
        if ($this->selectionStrategy === self::FIRST) {
            return reset($entities);
        }

        return match ($this->selectionStrategy) {
            self::MAX => $this->getEntityWithLatestHistory($entities),
            self::MIN => $this->getEntityWithEarliestHistory($entities),
            default => reset($entities),
        };
    }

    private function getEntityWithLatestHistory(array $entities): object
    {
        return array_reduce(
            $entities,
            function (?object $latestEntity, object $entity) {
                if ($latestEntity === null) {
                    return $entity;
                }

                $latestValidity = method_exists($latestEntity, 'getValidity')
                    ? $latestEntity->getValidity()->getValidTo()
                    : null;

                $currentValidity = method_exists($entity, 'getValidity')
                    ? $entity->getValidity()->getValidTo()
                    : null;

                return $this->validityUtil->gtNull($currentValidity, $latestValidity)
                    ? $entity
                    : $latestEntity;
            }
        );
    }

    private function getEntityWithEarliestHistory(array $entities): object
    {
        return array_reduce(
            $entities,
            function (?object $earliestEntity, object $entity) {
                if ($earliestEntity === null) {
                    return $entity;
                }

                $earliestValidity = method_exists($earliestEntity, 'getValidity')
                    ? $earliestEntity->getValidity()->getValidTo()
                    : null;

                $currentValidity = method_exists($entity, 'getValidity')
                    ? $entity->getValidity()->getValidTo()
                    : null;

                return $this->validityUtil->ltNull($currentValidity, $earliestValidity)
                    ? $entity
                    : $earliestEntity;
            }
        );
    }
}
