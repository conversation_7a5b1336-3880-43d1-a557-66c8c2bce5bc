<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use LoginAutonom\EtlBundle\Interfaces\FlowOneRowTransformerInterface;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\FlowTransformerTrait;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;

final class CopyAttributeFromEntityToRowTransformer implements FlowOneRowTransformerInterface
{
    use FlowTransformerTrait;
    use EtlCommonFunctionsTrait;

    public function __construct(
        private readonly string $entityField,
        private readonly array $fieldMap,
        private readonly PropertyAccessorInterface $propertyAccessor,
        private readonly bool $useClone
    ) {
    }

    public function transform(Row $row, FlowContext $context): Row
    {
        if ($this->isRowFinal($row)) {
            return $row;
        }
        if (!$row->has($this->entityField)) {
            return $row;
        }
        $entity = $row->valueOf($this->entityField);
        foreach ($this->fieldMap as $sourceField => $targetField) {
            if (!$this->propertyAccessor->isReadable($entity, $sourceField)) {
                continue;
            }
            $fieldValue = $this->propertyAccessor->getValue($entity, $sourceField);
            if (is_object($fieldValue) && $this->useClone) {
                $fieldValue = clone $fieldValue;
            }
            $row = $row->add(
                $context->entryFactory()->create($targetField, $fieldValue)
            );
        }
        return $row;
    }
}
