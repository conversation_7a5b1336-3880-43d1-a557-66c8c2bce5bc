<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Interfaces;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;

interface FlowPreProcessTransformerInterface extends FlowTransformerInterface
{
    public function collectInformation(Row $row, FlowContext $context, \ArrayObject $info): void;
    public function executeAction(\ArrayObject $info, FlowContext $context): void;
    public function buildRow(Row $row, FlowContext $context, \ArrayObject $info): Row;
}
