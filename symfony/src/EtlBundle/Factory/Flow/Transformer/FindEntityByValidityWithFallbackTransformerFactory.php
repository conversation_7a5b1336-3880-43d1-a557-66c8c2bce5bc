<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Factory\Flow\Transformer;

use LoginAutonom\DatabaseBundle\Interfaces\ValidityIntervalBasedEntitiesProviderInterface;
use LoginAutonom\EtlBundle\Flow\Transformer\FindEntityByValidityWithFallbackTransformer;
use LoginAutonom\EtlBundle\Interfaces\FlowTransformerFactoryInterface;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Contracts\Service\ServiceProviderInterface;

final readonly class FindEntityByValidityWithFallbackTransformerFactory implements
    FlowTransformerFactoryInterface
{
    public const PROVIDER = 'provider';
    public const IDENTIFIER_FIELD = 'identifierField';
    public const TARGET_ENTITY_FIELD = 'targetEntityField';
    public const ENTITY_CLASS = 'entityClass';
    public const FROM_FIELD = 'fromField';
    public const TARGET_VALID_FROM_FIELD = 'targetValidFromField';
    public const TARGET_VALID_TO_FIELD = 'targetValidToField';
    public const TO_FIELD = 'toField';
    public const FALLBACK_VALID_FROM_FIELD = 'fallbackValidFromField';
    public const FALLBACK_VALID_TO_FIELD = 'fallbackValidToField';

    public function __construct(
        #[TaggedLocator(ValidityIntervalBasedEntitiesProviderInterface::TAG, defaultIndexMethod: 'getName')]
        private ServiceProviderInterface $providers,
    ) {
    }

    public function build(array $config): object
    {
        $provider = $this->providers->get($config[self::PROVIDER]);
        $identifierField = $config[self::IDENTIFIER_FIELD];
        $targetEntityField = $config[self::TARGET_ENTITY_FIELD];
        $entityClass = $config[self::ENTITY_CLASS];
        $fromField = $config[self::FROM_FIELD];
        $targetValidFromField = $config[self::TARGET_VALID_FROM_FIELD];
        $targetValidToField = $config[self::TARGET_VALID_TO_FIELD] ?? null;
        $toField = $config[self::TO_FIELD] ?? null;
        $fallbackValidFromField = $config[self::FALLBACK_VALID_FROM_FIELD] ?? null;
        $fallbackValidToField = $config[self::FALLBACK_VALID_TO_FIELD] ?? null;

        return new FindEntityByValidityWithFallbackTransformer(
            $identifierField,
            $targetEntityField,
            $entityClass,
            $provider,
            $fromField,
            $targetValidFromField,
            $targetValidToField,
            $toField,
            $fallbackValidFromField,
            $fallbackValidToField
        );
    }

    public static function getName(): string
    {
        return 'find-entity-by-validity-with-fallback-transformer';
    }
}
