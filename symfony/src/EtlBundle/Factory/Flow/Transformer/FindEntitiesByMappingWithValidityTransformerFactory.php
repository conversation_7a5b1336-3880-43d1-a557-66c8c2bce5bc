<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Factory\Flow\Transformer;

use LoginAutonom\DatabaseBundle\Interfaces\ValidityIntervalBasedEntitiesProviderInterface;
use LoginAutonom\EtlBundle\Flow\Transformer\FindEntitiesByMappingWithValidityTransformer;
use LoginAutonom\EtlBundle\Interfaces\FlowTransformerFactoryInterface;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Contracts\Service\ServiceProviderInterface;

final readonly class FindEntitiesByMappingWithValidityTransformerFactory implements FlowTransformerFactoryInterface
{
    public function __construct(
        #[TaggedLocator(ValidityIntervalBasedEntitiesProviderInterface::TAG, defaultIndexMethod: 'getName')]
        private ServiceProviderInterface $providers,
    ) {
    }

    public function build(array $config): object
    {
        $provider = $this->providers->get($config['provider']);
        $mappingField = $config['mappingField'];
        $targetField = $config['targetField'];
        $entityClass = $config['entityClass'];
        $fromField = $config['fromField'] ?? null;
        $toField = $config['toField'] ?? null;
        return new FindEntitiesByMappingWithValidityTransformer(
            $mappingField,
            $targetField,
            $entityClass,
            $provider,
            $fromField,
            $toField
        );
    }

    public static function getName(): string
    {
        return 'find-entities-by-mapping-with-validity-transformer';
    }
}
