<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\DTO;

use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\EtlBundle\Entity\EtlMapping;

final class EtlMappingIdentifiersCache
{
    public function __construct(
        private array $cache = []
    ) {
    }

    public function find(array $identifiers): EtlMapping
    {
        foreach ($this->cache as [$cacheIdentifier, $mapping]) {
            if ($identifiers === $cacheIdentifier) {
                return $mapping;
            }
        }
        throw new NotFoundException(
            'Etl mapping cache',
            $identifiers
        );
    }

    public function add(EtlMapping $mapping): void
    {
        $this->cache[] = [$mapping->getSourceIdentifier(), $mapping];
    }

    public function clear(): void
    {
        $this->cache = [];
    }
}
