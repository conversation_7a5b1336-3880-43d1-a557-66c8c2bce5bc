<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\DTO;

use Flow\ETL\Rows;

final class RowsValidationDTO
{
    /**
     * @var ValidationErrorDTO[]
     */
    private array $errors = [];
    private array $identifiersToSkipLineCheck = [];
    private array $identifiersToRemoveLines = [];
    private bool $skipLoop = false;
    private bool $breakProcess = false;

    public function __construct(
        private readonly Rows $rows
    ) {
    }

    public function addError(ValidationErrorDTO $error): void
    {
        $this->errors[] = $error;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function hasErrors(): bool
    {
        return isset($this->errors) && $this->errors !== [];
    }

    public function getRows(): Rows
    {
        return $this->rows;
    }

    public function hasRows(): bool
    {
        return isset($this->rows);
    }

    public function addIdentifierToRemoveLine(string $identifier): void
    {
        $this->identifiersToRemoveLines[] = $identifier;
    }

    public function getIdentifiersToRemoveLines(): array
    {
        return $this->identifiersToRemoveLines;
    }

    public function hasIdentifiersToRemoveLines(): bool
    {
        return isset($this->identifiersToRemoveLines) && $this->identifiersToRemoveLines !== [];
    }

    public function addIdentifierToSkipLineCheck(string $identifier): void
    {
        $this->identifiersToSkipLineCheck[] = $identifier;
    }

    public function getIdentifiersToSkipLineCheck(): array
    {
        return $this->identifiersToSkipLineCheck;
    }

    public function hasIdentifiersToSkipLineCheck(): bool
    {
        return isset($this->identifiersToSkipLineCheck) && $this->identifiersToSkipLineCheck !== [];
    }

    public function isRemovable(string $identifier): bool
    {
        return in_array($identifier, $this->identifiersToRemoveLines, true);
    }

    public function canBeSkipped(string $identifier): bool
    {
        return in_array($identifier, $this->identifiersToSkipLineCheck, true);
    }

    public function setSkipLoop(): void
    {
        $this->skipLoop = true;
    }

    public function skipLoop(): bool
    {
        return $this->skipLoop;
    }

    public function setBreakProcess(): void
    {
        $this->breakProcess = true;
    }

    public function breakProcess(): bool
    {
        return $this->breakProcess;
    }
}
