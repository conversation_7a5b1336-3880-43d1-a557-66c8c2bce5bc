<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\DTO;

use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\EtlBundle\Entity\EtlMapping;

final class EtlMappingWorkflowCache
{
    /**
     * @var EtlMappingIdentifiersCache[]
     */
    private array $byMappingType;

    public function __construct(array $byMappingType = [])
    {
        $this->byMappingType = $byMappingType;
    }

    public function add(EtlMapping $mapping): void
    {
        if (!array_key_exists($mapping->getMappingType(), $this->byMappingType)) {
            $this->byMappingType[$mapping->getMappingType()] = $this->createNew();
        }
        $this->byMappingType[$mapping->getMappingType()]->add($mapping);
    }

    public function withMappingType(string $mappingType): EtlMappingIdentifiersCache
    {
        if (!array_key_exists($mappingType, $this->byMappingType)) {
            throw new NotFoundException('Etl mapping mappingType cache', $mappingType);
        }
        return $this->byMappingType[$mappingType];
    }

    private function createNew(): EtlMappingIdentifiersCache
    {
        return new EtlMappingIdentifiersCache();
    }
}
