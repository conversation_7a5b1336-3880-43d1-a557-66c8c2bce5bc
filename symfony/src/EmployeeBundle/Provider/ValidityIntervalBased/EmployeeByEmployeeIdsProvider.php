<?php

declare(strict_types=1);

namespace LoginAutonom\EmployeeBundle\Provider\ValidityIntervalBased;

use Carbon\CarbonImmutable;
use LoginAutonom\CoreBundle\Interfaces\QueryBusInterface;
use LoginAutonom\DatabaseBundle\DTO\IdsWithValidityInterval;
use LoginAutonom\DatabaseBundle\DTO\ValidityIntervalBasedEntities;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityIntervalBasedEntitiesProviderInterface;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoValidityStamp;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\ValidityIntervalStamp;
use LoginAutonom\EmployeeBundle\Message\Query\EmployeeByEmployeeIdsQuery;

final readonly class EmployeeByEmployeeIdsProvider implements ValidityIntervalBasedEntitiesProviderInterface
{
    public function __construct(
        private QueryBusInterface $queryBus,
    ) {
    }

    public function provide(
        IdsWithValidityInterval $dto,
        array $stamps,
        ValidityIntervalBasedEntities $entities
    ): ValidityIntervalBasedEntities {
        if ($dto->hasFrom()) {
            $stamps[] = new ValidityIntervalStamp(
                $dto->getFrom(),
                $dto->getTo() ?? CarbonImmutable::now(),
            );
        } else {
            $stamps[] = new NoValidityStamp();
        }
        $entitiesFromQuery = $this->queryBus->query(
            (new EmployeeByEmployeeIdsQuery())
                ->setEmployeeIds($dto->getIds()),
            $stamps
        );
        $entities->addMultiple($entitiesFromQuery);

        return $entities;
    }

    public static function getName(): string
    {
        return 'employee-by-employee-ids';
    }
}
