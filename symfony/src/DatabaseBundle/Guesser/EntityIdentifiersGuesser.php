<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Guesser;

use LoginAutonom\DatabaseBundle\Provider\EntityMetadataProvider;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;

final readonly class EntityIdentifiersGuesser
{
    public function __construct(
        private EntityMetadataProvider $entityMetadataProvider,
        private PropertyAccessorInterface $propertyAccessor,
    ) {
    }

    public function guess(object $entity): array
    {
        $metadata = $this->entityMetadataProvider->provide($entity);
        $identifiers = [];
        foreach ($metadata->getEntityIdentifiers() as $entityIdentifier) {
            $identifierField = $entityIdentifier->identifier;
            if (!$this->propertyAccessor->isReadable($entity, $identifierField)) {
                $identifiers[$identifierField] = null;
            }
            $identifiers[$identifierField] = $this->propertyAccessor->getValue($entity, $identifierField);
        }

        return $identifiers;
    }
}
