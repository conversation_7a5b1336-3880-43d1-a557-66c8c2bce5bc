<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Interfaces;

use LoginAutonom\CoreBundle\Interfaces\ValidityIntervalBasedObjectStorageInterface;

interface ValidityBasedObjectsInterface
{
    public function addMultiple(array $objects): void;

    public function add(array $identifiers, object $entity): void;

    public function create(array $identifiers): ValidityIntervalBasedObjectStorageInterface;

    public function get(mixed $identifiers): ValidityIntervalBasedObjectStorageInterface;

    public function has(mixed $identifiers): bool;

    public function clear(): void;

    public function all(): array;

    public function setFinalIdentifiers(array $identifiers): void;

    public function isFinalIdentifiers(array $identifiers): bool;
}
