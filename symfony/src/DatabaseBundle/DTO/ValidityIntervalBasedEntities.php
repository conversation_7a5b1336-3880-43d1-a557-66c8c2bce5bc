<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\DTO;

use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\CoreBundle\Util\ArrayUtil;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectsInterface;

final class ValidityIntervalBasedEntities implements ValidityBasedObjectsInterface
{
    /**
     * @var ValidityIntervalBasedEntity[]
     */
    private array $entitiesByHash = [];
    private array $identifiersByHash = [];
    private array $finalIdentifiers = [];
    private ArrayUtil $arrayUtil;

    public function __construct(
        private readonly string $entityClass,
        private readonly \Closure $identifierCallback,
        private readonly \Closure $createEntityDtoCallback,
        private readonly \Closure $addNewEntityCallback,
    ) {
        $this->arrayUtil = new ArrayUtil();
    }

    public function addMultiple(array $objects): void
    {
        foreach ($objects as $entity) {
            $identifiers = $this->identifierCallback->__invoke($entity);
            $this->add($identifiers, $entity);
        }
    }

    public function add(array $identifiers, object $entity): void
    {
        try {
            $validityIntervalBasedEntity = $this->get($identifiers);
        } catch (NotFoundException) {
            $validityIntervalBasedEntity = $this->create($identifiers);
        }
        $validityIntervalBasedEntity->add($entity);
        ($this->addNewEntityCallback)($identifiers, $entity);
    }

    public function create(array $identifiers): ValidityIntervalBasedEntity
    {
        $validityIntervalBasedEntity = ($this->createEntityDtoCallback)($identifiers);
        $hash = $this->createHash($validityIntervalBasedEntity);
        $this->identifiersByHash[$hash] = $identifiers;
        $this->entitiesByHash[$hash] = $validityIntervalBasedEntity;

        return $validityIntervalBasedEntity;
    }

    public function get(mixed $identifiers): ValidityIntervalBasedEntity
    {
        if (is_object($identifiers)) {
            $identifiers = $this->identifierCallback->__invoke($identifiers);
        }
        if (!$this->has($identifiers)) {
            throw new NotFoundException(
                self::class . ' - ' . $this->entityClass,
                $identifiers,
                'Entity not found with identifier!'
            );
        }
        $hash = $this->findHashByIdentifiers($identifiers);

        return $this->entitiesByHash[$hash];
    }

    public function has(mixed $identifiers): bool
    {
        if (empty($identifiers)) {
            return false;
        }
        if (is_object($identifiers)) {
            $identifiers = $this->identifierCallback->__invoke($identifiers);
        }
        if (!$this->hasHashByIdentifiers($identifiers)) {
            return false;
        }
        $hash = $this->findHashByIdentifiers($identifiers);

        return array_key_exists($hash, $this->entitiesByHash);
    }

    public function clear(): void
    {
        $this->entitiesByHash = [];
        $this->identifiersByHash = [];
    }

    public function all(): array
    {
        return $this->entitiesByHash;
    }

    public function setFinalIdentifiers(array $identifiers): void
    {
        if (in_array($identifiers, $this->finalIdentifiers, true)) {
            return;
        }
        $this->finalIdentifiers[] = $identifiers;
    }

    public function isFinalIdentifiers(array $identifiers): bool
    {
        return in_array($identifiers, $this->finalIdentifiers, true);
    }

    private function createHash(object $entity): string
    {
        return spl_object_hash($entity);
    }

    private function findHashByIdentifiers(array $identifiers): string
    {
        return $this->arrayUtil->findArrayInArray($identifiers, $this->identifiersByHash);
    }

    private function hasHashByIdentifiers(array $identifiers): bool
    {
        try {
            $this->findHashByIdentifiers($identifiers);
            return true;
        } catch (NotFoundException) {
            return false;
        }
    }
}
