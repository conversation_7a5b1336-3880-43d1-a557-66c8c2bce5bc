<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Guesser;

use Carbon\Carbon;
use LoginAutonom\CoreBundle\Enum\DateRangePatternEnum;

final readonly class DateRangeGuesser
{
    private const DATETIME_FORMAT = 'Y-m-d H:i:s';

    public function __construct(
        private int $defaultMaxMonths = 12
    ) {
    }

    public function guess(array $config): array
    {
        $pattern = $config[DateRangePatternEnum::DATE_PATTERN->value] ?? DateRangePatternEnum::CURRENT_MONTH->value;
        $maxMonths = $config[DateRangePatternEnum::MAX_MONTHS->value] ?? $this->defaultMaxMonths;
        $baseDate = isset($config[DateRangePatternEnum::BASE_DATE->value])
            ? Carbon::parse($config[DateRangePatternEnum::BASE_DATE->value])
            : Carbon::now();

        return match ($pattern) {
            DateRangePatternEnum::CURRENT_DATE->value => $this->getCurrentDateRange($baseDate),
            DateRangePatternEnum::NEXT_MONTH->value => $this->getNextMonthRange($baseDate),
            DateRangePatternEnum::PREV_MONTH->value => $this->getPrevMonthRange($baseDate),
            DateRangePatternEnum::LAST_N_MONTHS->value => $this->getLastNMonthsRange($baseDate, $maxMonths),
            DateRangePatternEnum::NEXT_N_MONTHS->value => $this->getNextNMonthsRange($baseDate, $maxMonths),
            DateRangePatternEnum::CUSTOM_RANGE->value => $this->getCustomRange($config),
            default => $this->getCurrentMonthRange($baseDate),
        };
    }

    /**
     * Get current date range (same day)
     */
    private function getCurrentDateRange(Carbon $baseDate): array
    {
        $startOfDay = $baseDate->copy()->startOfDay();
        $endOfDay = $baseDate->copy()->endOfDay();

        return [
            DateRangePatternEnum::FROM->value => $startOfDay->format(self::DATETIME_FORMAT),
            DateRangePatternEnum::TO->value => $endOfDay->format(self::DATETIME_FORMAT),
        ];
    }

    /**
     * Get current month range
     */
    private function getCurrentMonthRange(Carbon $baseDate): array
    {
        $startOfMonth = $baseDate->copy()->startOfMonth();
        $endOfMonth = $baseDate->copy()->endOfMonth();

        return [
            DateRangePatternEnum::FROM->value => $startOfMonth->format(self::DATETIME_FORMAT),
            DateRangePatternEnum::TO->value => $endOfMonth->format(self::DATETIME_FORMAT),
        ];
    }

    /**
     * Get next month range
     */
    private function getNextMonthRange(Carbon $baseDate): array
    {
        $startOfMonth = $baseDate->copy()->addMonth()->startOfMonth();
        $endOfMonth = $baseDate->copy()->addMonth()->endOfMonth();

        return [
            DateRangePatternEnum::FROM->value => $startOfMonth->format(self::DATETIME_FORMAT),
            DateRangePatternEnum::TO->value => $endOfMonth->format(self::DATETIME_FORMAT),
        ];
    }

    /**
     * Get previous month range
     */
    private function getPrevMonthRange(Carbon $baseDate): array
    {
        $startOfMonth = $baseDate->copy()->subMonth()->startOfMonth();
        $endOfMonth = $baseDate->copy()->subMonth()->endOfMonth();

        return [
            DateRangePatternEnum::FROM->value => $startOfMonth->format(self::DATETIME_FORMAT),
            DateRangePatternEnum::TO->value => $endOfMonth->format(self::DATETIME_FORMAT),
        ];
    }

    /**
     * Get last N months range
     */
    private function getLastNMonthsRange(Carbon $baseDate, int $months): array
    {
        $months = max(1, min($months, $this->defaultMaxMonths));

        $endOfRange = $baseDate->copy()->endOfMonth();
        $startOfRange = $baseDate->copy()->subMonths($months)->startOfMonth();

        return [
            DateRangePatternEnum::FROM->value => $startOfRange->format(self::DATETIME_FORMAT),
            DateRangePatternEnum::TO->value => $endOfRange->format(self::DATETIME_FORMAT),
        ];
    }

    /**
     * Get next N months range
     */
    private function getNextNMonthsRange(Carbon $baseDate, int $months): array
    {
        $months = max(1, min($months, $this->defaultMaxMonths));

        $startOfRange = $baseDate->copy()->addMonth()->startOfMonth();
        $endOfRange = $baseDate->copy()->addMonths($months)->endOfMonth();

        return [
            DateRangePatternEnum::FROM->value => $startOfRange->format(self::DATETIME_FORMAT),
            DateRangePatternEnum::TO->value => $endOfRange->format(self::DATETIME_FORMAT),
        ];
    }

    /**
     * Get custom date range from configuration
     */
    private function getCustomRange(array $config): array
    {
        $from = $config[DateRangePatternEnum::CUSTOM_FROM->value] ?? null;
        $to = $config[DateRangePatternEnum::CUSTOM_TO->value] ?? null;

        if (!$from || !$to) {
            throw new \InvalidArgumentException('Custom range requires both from and to dates');
        }

        $fromDate = Carbon::parse($from);
        $toDate = Carbon::parse($to);

        $monthsDiff = $fromDate->diffInMonths($toDate);
        if ($monthsDiff > $this->defaultMaxMonths) {
            throw new \InvalidArgumentException(
                "Custom range exceeds maximum allowed months: {$this->defaultMaxMonths}"
            );
        }

        return [
            DateRangePatternEnum::FROM->value => $fromDate->format(self::DATETIME_FORMAT),
            DateRangePatternEnum::TO->value => $toDate->format(self::DATETIME_FORMAT),
        ];
    }
}
