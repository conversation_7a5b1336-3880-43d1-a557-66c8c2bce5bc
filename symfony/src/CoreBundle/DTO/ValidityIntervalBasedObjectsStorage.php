<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\DTO;

use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\CoreBundle\Interfaces\ValidityIntervalBasedDataInterface;
use LoginAutonom\CoreBundle\Interfaces\ValidityIntervalBasedObjectStorageInterface;
use LoginAutonom\CoreBundle\Util\ArrayUtil;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectsInterface;

final class ValidityIntervalBasedObjectsStorage implements ValidityBasedObjectsInterface
{
    private array $objectsByHash = [];
    private array $identifiersByHash = [];

    public function __construct(
        private readonly ArrayUtil $arrayUtil
    ) {
    }

    public function addMultiple(array $objects): void
    {
        foreach ($objects as $object) {
            if (!$object instanceof ValidityIntervalBasedDataInterface) {
                continue;
            }
            $this->add($object->getIdentifiers(), $object);
        }
    }

    public function add(array $identifiers, object $entity): void
    {
        if (!$entity instanceof ValidityIntervalBasedDataInterface) {
            throw new \InvalidArgumentException("Object is invalid");
        }
        try {
            $storage = $this->get($identifiers);
        } catch (NotFoundException) {
            $storage = $this->create($identifiers);
        }
        $storage->add($entity);
    }

    public function get(mixed $identifiers): ValidityIntervalBasedObjectStorageInterface
    {
        if (!$this->has($identifiers)) {
            throw new NotFoundException(
                self::class,
                $identifiers,
                'Entity not found with identifier!'
            );
        }
        $hash = $this->findHashByIdentifiers($identifiers);

        return $this->objectsByHash[$hash];
    }

    public function create(array $identifiers): ValidityIntervalBasedObjectStorageInterface
    {
        $storage = new ValidityIntervalBasedObjectStorage($identifiers);
        $hash = $this->generateHash($storage);
        $this->identifiersByHash[$hash] = $identifiers;
        $this->objectsByHash[$hash] = $storage;

        return $storage;
    }

    private function generateHash(object $object): string
    {
        return spl_object_hash($object);
    }

    public function has(mixed $identifiers): bool
    {
        try {
            $hash = $this->findHashByIdentifiers($identifiers);
            return true;
        } catch (NotFoundException) {
            return false;
        }
    }

    public function clear(): void
    {
        $this->objectsByHash = [];
        $this->identifiersByHash = [];
    }

    public function all(): array
    {
        return $this->objectsByHash;
    }

    public function setFinalIdentifiers(array $identifiers): void
    {
        throw new \Exception('Not implemented!');
    }

    public function isFinalIdentifiers(array $identifiers): bool
    {
        throw new \Exception('Not implemented!');
    }

    /**
     * @param array $identifiers
     * @return string
     * @throws NotFoundException
     */
    private function findHashByIdentifiers(array $identifiers): string
    {
        return $this->arrayUtil->findArrayInArray($identifiers, $this->identifiersByHash);
    }
}
