<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Util;

use LoginAutonom\CoreBundle\Exception\NotHandledMessageException;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Stamp\HandledStamp;

final class ResultExtractorFromMessageEnvelope
{
    public function extract(Envelope $envelope): mixed
    {
        $stamp = $envelope->last(HandledStamp::class);
        if ($stamp === null) {
            throw new NotHandledMessageException();
        }
        return $stamp->getResult();
    }
}
