<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Util;

use LoginAutonom\CoreBundle\Enum\BuiltinTypeEnum;
use LoginAutonom\CoreBundle\Exception\NotFoundException;

final class AnnotationHelper
{
    public function hasProperty(string|object $class, string $propertyName): bool
    {
        $reflection = new \ReflectionClass($class);
        return $reflection->hasProperty($propertyName);
    }

    public function findPropertiesByAttribute(string|object $class, string $attributeClass): array
    {
        $reflection = new \ReflectionClass($class);
        $found = [];
        foreach ($reflection->getProperties() as $property) {
            $attributes = $property->getAttributes();
            if ($attributes === []) {
                continue;
            }
            $instances = [];
            foreach ($attributes as $attribute) {
                $instance = $attribute->newInstance();
                if (!$instance instanceof $attributeClass) {
                    continue;
                }
                $instances[] = $instance;
            }
            if ($instances === []) {
                continue;
            }
            $found[$property->getName()] = $instances;
        }

        return $found;
    }

    public function findAttributeInProperty(
        string|object $class,
        string $propertyName,
        string $attributeClass
    ): object {
        $attributes = $this->findAttributesInProperty($class, $propertyName, $attributeClass);
        if ($attributes === []) {
            throw new NotFoundException(get_class($class) . ":{$propertyName}", $attributeClass);
        }
        /** @var \ReflectionAttribute $attribute */
        $attribute = reset($attributes);

        return $attribute;
    }

    public function findAttributesInProperty(
        string|object $class,
        string $propertyName,
        string $attributeClass
    ): array {
        $reflection = new \ReflectionClass($class);
        $property = $reflection->getProperty($propertyName);
        return $property->getAttributes($attributeClass);
    }

    public function hasAttributeInProperty(
        string|object $class,
        string $propertyName,
        string $attributeClass
    ): bool {
        $attributes = $this->findAttributesInProperty($class, $propertyName, $attributeClass);
        return $attributes !== [];
    }

    public function hasClassAttribute(string|object $class, string $attributeClass): bool
    {
        return $this->findClassAttribute($class, $attributeClass) !== [];
    }

    public function findClassAttribute(string|object $class, string $attributeClass): array
    {
        $reflection = new \ReflectionClass($class);
        $found = [];
        foreach ($reflection->getAttributes($attributeClass) as $attribute) {
            $found[] = $attribute->newInstance();
        }

        return $found;
    }

    public function isBackedEnum(string $class): bool
    {
        if (!enum_exists($class)) {
            return false;
        }
        $reflection = new \ReflectionEnum($class);
        return $reflection->isBacked();
    }

    public function isPropertyType(string|object $class, string $propertyName, BuiltinTypeEnum $type): bool
    {
        $reflection = new \ReflectionClass($class);
        $property = $reflection->getProperty($propertyName);
        $propertyType = $property->getType();
        if (
            isset($propertyType) &&
            $propertyType instanceof \ReflectionNamedType &&
            $propertyType->getName() === $type->value
        ) {
            return true;
        } elseif (isset($propertyType) && $propertyType instanceof \ReflectionIntersectionType) {
            $unionTypes = $propertyType->getTypes();
            foreach ($unionTypes as $unionType) {
                if ($unionType->getType() === $type->value) {
                    return true;
                }
            }
        }
        return false;
    }
}
