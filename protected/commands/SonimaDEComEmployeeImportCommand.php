<?php

declare(strict_types=1);
const DS = DIRECTORY_SEPARATOR;

use Components\Core\Command\AbstractUserSessionCommand;
use phpseclib3\Net\SFTP;

$composerAutoloder = include_once('../vendor/autoload.php');
Yang::registerAutoloader([$composerAutoloder, 'loadClass'], true);
Yang::loadComponentNamespaces('Grid2Core');
Yang::loadComponentNamespaces('Core');

class SonimaDEComEmployeeImportCommand extends AbstractUserSessionCommand
{
    public const VALID_FROM = '1915-01-01';
    public const ALL = 'ALL';
    public const WAGE_TYPE = 'MoGe';
    public const CONTRACT_NUMBER = '1';
    public const STRING_NULL = 'null';
    public const MAX_LENGTH = '32';
    private const TRANSFER_EXPORT = '/transfer/Export/';
    private const USER = 'user';
    private const PASS = 'pass';
    private const EXTENSION = 'extension';
    private const FILENAME = 'filename';
    private const HOST = 'host';
    private int $publishedStatus;
    private string $defaultEnd;
    private string $createdBy;
    private string $createdOn;
    private array $costIdUnitMapping = [
        '1000' => '2ac977a07147dc6f174643cd74a34a8e', // Verwaltung
        '1020' => '23040d9e4b6d8a99a39e113bd80b83bc', // Geschäftsführung
        '1030' => 'b24aa7a32f9f6605f289521d123f2452', // Vertrieb Innendienst
        '1035' => '07184cf37ccfbe3b4d4efa8e1474013a', // Vertrieb Außendienst, Projektmanagement
        '1040' => '13eeb8e7520af7838bd0d517ec24a8be', // IT
        '1050' => '4b4f0f4ba73e162c76572a9d86142674', // Finanzen, Controllig, Einkauf, Export
        '1060' => '5787a42a45a7086693451a54e01411df', // Personalwesen
        '1065' => '7ee402b2384bc3230229556d304c5323', // Betriebsrat
        '1070' => 'f1b5948d9a3ff38d9d01150ca6f2518f', // Qualitätsmanagement
        '1090' => '262b3816442723b63a3f3dd6e2cfa99c', // Zoll
        '2000' => '5adbbfa6beb5f9711e2c1aa88f56147c', // Betrieb, operativ
        '2120' => 'd4874b3745408066c0cd199b9301c528', // Classic | Umpacken
        '2130' => 'dec318e97b3d885011df99afe571f981', // Classic | Montage
        '2140' => '82b6324f2a378b29b67d9e54f2fe5719', // Classic | Waschen
        '2200' => '2d69a6588e8508ea57564712e5e7b002', // Classic | Qualitätsservice
        '2150' => 'df0a06c8ee83d19de76477ecb45557a8', // Demontage = Remanufacturing
        '2300' => '2e09a136f387d40e48e415322b1e47ea', // Logistik
        '2700' => '768a381ef8f77d88262e6ba53f212c97', // Qualitätssicherung
        '2800' => '630f0303a4261a9f53ff19a0673ff0c1', // Instandhaltung
        '3000' => '6ebb647679aeca0b0f8313a60e24310a', // Betrieb, operativ PH (Philippsburg)
        '3120' => 'a86af5d94237622d3c29da15bbe5a1ca', // Umpacken (Philippsburg Umpacken)
        '3140' => '78d423a92f8325c69743e12bcd621db8', // Jungheinrich
        '3150' => 'da9b2cc4a8105df9df2714970e384e03', // DeWab
        '3160' => '4e1db2254e7ba3e2e2962e8653012f2f', // Remanufacturing (Philippsburg)
        '3300' => '2e09a136f387d40e48e415322b1e47ea', // Logistik (ugyanaz, mint 2300)
        '7000' => 'ecb51007d6c705bc3a0fbd4408508e6c', // SONIMA AB, Schweden
        '7010' => '73b85e9909b7a35e5c8d8de0e8786f92', // SONIMA Kft, Ungarn
        '7030' => '7852fc7b5f5f4621e5d3f81dc7a12617', // SONIMA sp. z o.o., Polen
        '7040' => 'fdc5b221243b9a35927848c3d5c79de2', // PCL Erding GmbH
    ];

    public function actionRun(): void
    {
        $_SESSION['tiptime']['rootSession'] = true;
        $customerFtpFolderName = App::getSetting('customerFtpFolderName');
        $sftpConnections = Yang::getParam('additionalComponents')['sftp'];
        $this->defaultEnd = App::getSetting('defaultEnd');
        $this->publishedStatus = Status::PUBLISHED;
        $this->createdBy = 'SyncSonimaDE';
        $this->createdOn = date('Y-m-d H:i:s');

        Yang::log('START data import ' . date('Y-m-d H:i:s'), 'log', 'system.Import');

        $localPath = Yang::getBasePath() . DS . '..' . DS . 'webroot' . DS . 'file_storage' . DS . 'sonima_de_com' .
            DS . 'employee' . DS;

        $this->ensureLocalDirectoryExists($localPath);

        $sftp = $this->connectToSftp($sftpConnections);
        if (!$sftp) {
            Yang::log('SFTP connection failed', 'log', 'system.Import');
            return;
        }

        $sftpFiles = $this->listSftpFiles($sftp);
        if (empty($sftpFiles)) {
            Yang::log('No files found on SFTP', 'log', 'system.Import');
            $sftp->disconnect();
            return;
        }

        $processedFiles = [];
        $failedFiles = [];

        foreach ($sftpFiles as $fileName) {
            try {
                Yang::log('Processing file: ' . $fileName, 'log', 'system.Import');
                if (!$this->downloadFileFromSftp($sftp, $fileName, $localPath)) {
                    Yang::log('Failed to download file: ' . $fileName, 'log', 'system.Import');
                    $failedFiles[] = $fileName;
                    continue;
                }
            } catch (Exception $ex) {
                Yang::log('Error downloading file ' . $fileName . ': ' . $ex->getMessage(), 'log', 'system.Import');
                $failedFiles[] = $fileName;
                continue;
            }

            if (str_contains($fileName, '.csv')) {
                try {
                    $employeeData = $this->processData($localPath, $fileName);
                    $this->processEmployeeData($employeeData);
                } catch (Exception $ex) {
                    Yang::log(
                        'Error processing CSV data for ' . $fileName . ': ' . $ex->getMessage(),
                        'log',
                        'system.Import'
                    );
                    $failedFiles[] = $fileName;
                    continue;
                }

                try {
                    Yang::log('FILE PROCESSED: ' . $fileName, 'log', 'system.Import');
                    $this->archiveFiles($localPath, $fileName, $customerFtpFolderName);
                    $processedFiles[] = $fileName;
                } catch (Exception $ex) {
                    Yang::log('Error archiving file ' . $fileName . ': ' . $ex->getMessage(), 'log', 'system.Import');
                    $failedFiles[] = $fileName;
                }
            }
        }

        foreach ($processedFiles as $fileName) {
            $this->deleteFileFromSftp($sftp, $fileName);
        }

        $sftp->disconnect();

        Yang::log(
            'Import completed. Processed: ' . count($processedFiles) . ', Failed: ' . count($failedFiles),
            'log',
            'system.Import'
        );
    }

    private function processData(string $filePath, string $fileName): array
    {
        $employee = [];
        $csvToArray = new CsvToArray();
        $dataArray = $csvToArray
            ->setSeparator(',')
            ->getCsvToArray(
                $filePath,
                $fileName
            );

        if (count($dataArray)) {
            for ($i = 0; $i <= count($dataArray); $i++) {
                if (!isset($dataArray[$i][0]) || !preg_match('/^\d+$/', trim($dataArray[$i][0]))) {
                    unset($dataArray[$i]);
                    continue;
                }

                $employee[$i]['emp_id'] = preg_replace('/[^0-9]/', '', $dataArray[$i][0]);
                $employee[$i]['last_name'] = trim($dataArray[$i][1]);
                $employee[$i]['first_name'] = trim($dataArray[$i][2]);
                $employee[$i]['card'] = trim($dataArray[$i][3]);
                $employee[$i]['cost_id'] = trim($dataArray[$i][4]);

                try {
                    if (!empty($dataArray[$i][5])) {
                        $date = new DateTime(trim($dataArray[$i][5]));
                        $employee[$i]['valid_from'] = $date->format('Y-m-d');
                    }
                } catch (Exception $ex) {
                    echo $ex->getMessage();
                }

                try {
                    if (trim($dataArray[$i][6]) !== self::STRING_NULL) {
                        $date = new DateTime(trim($dataArray[$i][6]));
                        $employee[$i]['valid_to'] = $date->format('Y-m-d');
                    } else {
                        $employee[$i]['valid_to'] = $this->defaultEnd;
                    }
                } catch (Exception $ex) {
                    echo $ex->getMessage();
                }
            }

            return $employee;
        } else {
            Yang::log('NO DATA IN FILE', 'log', 'system.Import');
            return [];
        }
    }

    private function processEmployeeData(array $employeeData): void
    {
        $this->dropAndCreateTempTable('temp_employee_import', 'employee');
        $this->dropAndCreateTempTable('temp_employee_contract_import', 'employee_contract');
        $this->dropAndCreateTempTable('temp_employee_card_import', 'employee_card');
        $this->dropAndCreateTempTable('temp_employee_cost_import', 'employee_cost');
        $this->dropAndCreateTempTable('temp_employee_group_import', 'employee_group');

        $dbCompany = $this->getCompanyId();

        $employeeInserts = [];
        $contractInserts = [];
        $cardInserts = [];
        $costInserts = [];
        $employeeGroupInserts = [];
        $costId = '';
        $costCenterId = '';

        foreach ($employeeData as $values) {
            $employeeContractId = $values['emp_id'] . self::CONTRACT_NUMBER;
            if (isset($values['cost_id'])) {
                $costId = $this->checkIfCostExist($values['cost_id']);
                $costCenterId = $this->checkIfCostCenterExist($values['cost_id']);
                if (array_key_exists($values['cost_id'], $this->costIdUnitMapping)) {
                    $unitId = $this->costIdUnitMapping[$values['cost_id']];
                    $employeeGroupInserts[] = "('" . $employeeContractId . "', '" . 'unit_id' . "', '" . $unitId . "', '" . $values['valid_from'] . "', '" . $values['valid_to'] . "', '" . $this->publishedStatus . "', '" . $this->createdBy . "', '" . $this->createdOn . "')";
                }
            }

            $employeeInserts[] = "('" . $values['emp_id'] . "', '" . $dbCompany['company_id'] . "', '" . $values['emp_id'] . "', '" . $values['first_name'] . "', '" . $values['last_name'] . "', '" . $values['valid_from'] . "', '" . $values['valid_to'] . "', '" . $this->publishedStatus . "', '" . $this->createdBy . "', '" . $this->createdOn . "')";

            $contractInserts[] = "('" . $employeeContractId . "', '" . $values['emp_id'] . "', '" . self::CONTRACT_NUMBER . "', '" . $values['valid_from'] . "', '" . $values['valid_to'] . "', '" . self::WAGE_TYPE . "', '" . $values['valid_from'] . "', '" . $values['valid_to'] . "', '" . $this->publishedStatus . "', '" . $this->createdBy . "', '" . $this->createdOn . "')";

            $cardInserts[] = "('" . $employeeContractId . "', '" . $values['card'] . "', '" . $values['valid_from'] . "', '" . $values['valid_to'] . "', '" . $this->publishedStatus . "', '" . $this->createdBy . "', '" . $this->createdOn . "')";

            $costInserts[] = "('" . $employeeContractId . "', '" . $costId . "', '" . $costCenterId . "', '" . $values['valid_from'] . "', '" . $values['valid_to'] . "', '" . $this->publishedStatus . "', '" . $this->createdBy . "', '" . $this->createdOn . "')";
        }

        $this->executeSqlInserts($employeeInserts, $contractInserts, $cardInserts, $costInserts, $employeeGroupInserts);

        $dbSyncEmployee = new DbSync();
        $dbSyncEmployee->run('SonimaComDESyncEmployee');

        $dbSyncEmployeeContract = new DbSync();
        $dbSyncEmployeeContract->run('SonimaComDESyncEmployeeContract');

        $dbSyncEmployeeCard = new DbSync();
        $dbSyncEmployeeCard->run('SonimaComDESyncEmployeeCard');

        $dbSyncEmployeeCost = new DbSync();
        $dbSyncEmployeeCost->run('SonimaComDESyncEmployeeCost');

        $dbSyncEmployeeGroup = new DbSync();
        $dbSyncEmployeeGroup->run('SonimaComDESyncEmployeeGroup');
    }

    private function checkIfCostExist(string $costName): string
    {
        $cost = new Cost();
        $criteria = new CDbCriteria();
        $criteria->condition = "`cost_name` = '$costName'
							AND `status` = " . $this->publishedStatus . '
							AND CURDATE() BETWEEN `valid_from` AND `valid_to`';

        $existCostData = $cost->find($criteria);

        if ($existCostData) {
            return $existCostData->cost_id;
        } else {
            $c = new Cost();
            $c->cost_id = strlen($costName) <= self::MAX_LENGTH ? $costName : App::getIncreasedGlobalIdentifier(
                $costName
            );
            $c->cost_name = $costName;
            $c->company_id = self::ALL;
            $c->payroll_id = self::ALL;
            $c->status = $this->publishedStatus;
            $c->valid_from = self::VALID_FROM;
            $c->valid_to = $this->defaultEnd;
            $c->created_by = $this->createdBy;
            $c->created_on = $this->createdOn;
            $c->save();
            return $c->cost_id;
        }
    }

    private function checkIfCostCenterExist(string $costCenterName): string
    {
        $costCenter = new CostCenter();
        $criteria = new CDbCriteria();
        $criteria->condition = "`cost_center_name` = '$costCenterName' 
							AND `status` = " . $this->publishedStatus . ' 
							AND CURDATE() BETWEEN `valid_from` AND `valid_to`';

        $existCostCenterData = $costCenter->find($criteria);

        if ($existCostCenterData) {
            return $existCostCenterData->cost_center_id;
        } else {
            $cc = new CostCenter();
            $cc->cost_center_id = strlen(
                $costCenterName
            ) <= self::MAX_LENGTH ? $costCenterName : App::getIncreasedGlobalIdentifier($costCenterName);
            $cc->cost_center_name = $costCenterName;
            $cc->company_id = self::ALL;
            $cc->payroll_id = self::ALL;
            $cc->status = $this->publishedStatus;
            $cc->valid_from = self::VALID_FROM;
            $cc->valid_to = $this->defaultEnd;
            $cc->created_by = $this->createdBy;
            $cc->created_on = $this->createdOn;
            $cc->save();
            return $cc->cost_center_id;
        }
    }

    private function getCompanyId()
    {
        $SQL = "SELECT `company_id`, `company_name`
				FROM `company`
				WHERE `status` = '$this->publishedStatus' AND `company_name` = 'Sonima DE';
                ";

        return dbFetchRow($SQL);
    }

    private function dropAndCreateTempTable(string $tempTable, string $table): void
    {
        $dropTempTableSql = "DROP TABLE IF EXISTS $tempTable;";
        dbExecute($dropTempTableSql);
        $createTempTableSql = "
			CREATE TABLE $tempTable LIKE $table";
        dbExecute($createTempTableSql);
    }

    private function checkFile(string $file): bool
    {
        if (file_exists($file)) {
            return true;
        } else {
            return false;
        }
    }

    private function archiveFiles(string $fileToArchive, string $fileName, string $customerFtpFolderName): void
    {
        $path = Yang::getBasePath(
            ) . DS . '..' . DS . 'webroot' . DS . 'file_storage' . DS . 'autoImport' . DS . $customerFtpFolderName . DS . 'employee' . DS;

        $pathInfo = pathinfo($fileName);
        $extension = $pathInfo[self::EXTENSION];
        $baseFileName = $pathInfo[self::FILENAME];

        if (!file_exists($path) && !is_dir($path)) {
            mkdir($path, 0755, true);
        }

        $fileArchivePathName = $path . $baseFileName . '__' . date('Y-m-d') . '.' . $extension;
        $sourceFile = $fileToArchive . $fileName;

        if ($this->checkFile($sourceFile) === true) {
            copy($sourceFile, $fileArchivePathName);
            unlink($sourceFile);
        }

        // If more than 5 files delete oldest ones
        $files = glob($path . '*.*');
        if (count($files) > 5) {
            $oldest = '';
            $oldestTime = time();

            foreach ($files as $f) {
                $string = basename($f);
                if (strpos($string, '__') !== false) {
                    $parts = explode('__', $string);
                    $datePart = str_replace('.' . $extension, '', $parts[1]);
                    $fileTime = strtotime($datePart);

                    if ($fileTime < $oldestTime) {
                        $oldestTime = $fileTime;
                        $oldest = $f;
                    }
                }
            }

            if ($oldest && $this->checkFile($oldest) === true) {
                unlink($oldest);
            }
        }
    }

    private function connectToSftp(array $sftpConnections): ?SFTP
    {
        $sftp = new SFTP($sftpConnections[self::HOST]);

        if (!$sftp->login($sftpConnections[self::USER], $sftpConnections[self::PASS])) {
            Yang::log('SFTP login failed', 'log', 'system.Import');
            return null;
        }

        return $sftp;
    }

    private function listSftpFiles(SFTP $sftp): array
    {
        $sftpPath = self::TRANSFER_EXPORT;
        $files = [];

        if (!$sftp->chdir($sftpPath)) {
            Yang::log('Failed to change to SFTP directory: ' . $sftpPath, 'log', 'system.Import');
            return $files;
        }

        $fileList = $sftp->nlist();
        if ($fileList === false) {
            Yang::log('Failed to list SFTP directory contents', 'log', 'system.Import');
            return $files;
        }

        foreach ($fileList as $fileName) {
            if (preg_match('/^\d{8}_\d{6}_emplys\.csv$/', $fileName)) {
                $files[] = $fileName;
            }
        }

        return $files;
    }

    private function downloadFileFromSftp(SFTP $sftp, string $fileName, string $localPath): bool
    {
        $sftpPath = self::TRANSFER_EXPORT;
        $remoteFile = $sftpPath . $fileName;
        $localFile = $localPath . $fileName;

        $fileContent = $sftp->get($remoteFile);
        if ($fileContent === false) {
            Yang::log('Failed to download file from SFTP: ' . $remoteFile, 'log', 'system.Import');
            return false;
        }

        if (file_put_contents($localFile, $fileContent) === false) {
            Yang::log('Failed to save file locally: ' . $localFile, 'log', 'system.Import');
            return false;
        }

        return true;
    }

    private function ensureLocalDirectoryExists(string $path): void
    {
        if (!file_exists($path)) {
            mkdir($path, 0755, true);
            Yang::log('Created local directory: ' . $path, 'log', 'system.Import');
        }
    }

    public function deleteFileFromSftp(SFTP $sftp, string $fileName): bool
    {
        $sftpPath = self::TRANSFER_EXPORT;
        $result = $sftp->delete($sftpPath . $fileName);

        if ($result) {
            Yang::log('Successfully deleted file from SFTP: ' . $fileName, 'log', 'system.Import');
        } else {
            Yang::log('Failed to delete file from SFTP: ' . $fileName, 'log', 'system.Import');
        }

        return $result;
    }

    public function executeSqlInserts(
        array $employeeInserts,
        array $contractInserts,
        array $cardInserts,
        array $costInserts,
        array $employeeGroupInserts
    ): void {
        $SQL = 'INSERT INTO `temp_employee_import` (`employee_id`, `company_id`, `emp_id`, `first_name`, `last_name`, `valid_from`, `valid_to`, `status`, `created_by`, `created_on`) VALUES ' . implode(
                ', ',
                $employeeInserts
            ) . ";\n";

        $SQL .= 'INSERT INTO `temp_employee_contract_import` (`employee_contract_id`, `employee_id`, `employee_contract_number`, `ec_valid_from`, `ec_valid_to`, `wage_type`, `valid_from`, `valid_to`, `status`, `created_by`, `created_on`) VALUES ' . implode(
                ', ',
                $contractInserts
            ) . ";\n";

        $SQL .= 'INSERT INTO `temp_employee_card_import` (`employee_contract_id`, `card`, `valid_from`, `valid_to`, `status`, `created_by`, `created_on`) VALUES ' . implode(
                ', ',
                $cardInserts
            ) . ";\n";

        $SQL .= 'INSERT INTO `temp_employee_cost_import` (`employee_contract_id`, `cost_id`, `cost_center_id`, `valid_from`, `valid_to`, `status`, `created_by`, `created_on`) VALUES ' . implode(
                ', ',
                $costInserts
            ) . ";\n";

        if (!empty($employeeGroupInserts)) {
            $SQL .= 'INSERT INTO `temp_employee_group_import` (`employee_contract_id`, `group_id`, `group_value`, `valid_from`, `valid_to`, `status`, `created_by`, `created_on`) VALUES ' . implode(
                    ', ',
                    $employeeGroupInserts
                ) . ";\n";
        }

        dbExecute($SQL);
    }
}
